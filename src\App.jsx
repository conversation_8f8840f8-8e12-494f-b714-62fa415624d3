import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./auth/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import LoginPage from "./pages/LoginPage";
import DashboardLayout from "./pages/DashboardLayout";
import Analytics from "./pages/Analytics";
import Orders from "./pages/Orders";
import Wallet from "./pages/Wallet";
import TopProducts from "./pages/TopProducts";
import Customers from "./pages/Customers";
import AllOrders from "./pages/AllProducts";
import NotificationsTable from "./pages/LatestOrders";
import CustomerSupportTable from "./pages/Support";
import EmployeesTable from "./pages/EmployeesPage";
import CustomerListTable from "./pages/Customers";
import AssignDeliverymanTable from "./pages/AssignDeliverBoy";
import SignupPage from "./pages/SignUpPage";
import OTPPage from "./pages/otpPage";
// import EmployeesTable from "./pages/EmployeesPage";

function App() {
  return (
    // <AuthProvider>
    <Router>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/otp" element={<OTPPage />} />
        <Route path="/signup" element={<SignupPage />} />
        <Route path="dashboard" element={<DashboardLayout />}>
          <Route path="menu/admin" element={<Analytics />} />
          <Route path="menu/all-orders" element={<NotificationsTable />} />
          <Route path="menu/orders" element={<Orders />} />
          <Route path="menu/messages" element={<CustomerSupportTable />} />
          <Route path="menu/support-staff" element={<EmployeesTable />} />
          <Route path="menu/all-customers" element={<CustomerListTable />} />
          <Route
            path="menu/assign-delivery-boy"
            element={<AssignDeliverymanTable />}
          />
          <Route path="menu/manage-products" element={<AllOrders />} />
          <Route path="wallet" element={<Wallet />} />
          <Route path="top-products" element={<TopProducts />} />
          <Route path="customers" element={<Customers />} />
        </Route>

        <Route path="*" element={<DashboardLayout />} />
      </Routes>
    </Router>
    // </AuthProvider>
  );
}

export default App;
